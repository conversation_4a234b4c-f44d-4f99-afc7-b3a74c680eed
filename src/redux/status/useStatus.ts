import { useDispatch, useSelector } from 'react-redux'
import {
  getStatus,
  addStatus,
  updateStatus,
  updateTableStatus,
  deleteStatus,
  getOneStatus,
  setProgressTableColumnVisibilities,
  statusSorting,
  setProgressFilterValue,
  renamePhaseName,
  deletePhase,
  addPhaseName,
  setStatusById,
  setProgressTableColumnsWidth,
  setProgressTableColumnsOrder,
} from '.'
import { IAddPhasePayload, IDeletePhasePayload, IRenamePhasePayload } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useStatus = () => {
  const dispatch: AppDispatch = useDispatch()

  const formatStatusData = (data: any) => {
    const obj = {
      ...data,
      forecast_finish_date: data?.forecast_finish || data?.forecasted_end_date,
      planned_progress_percentage: data?.rev_plan_percentage || data?.revised_plan_percentage,
      actual_progress_percentage: data?.actual_percentage || data?.actual_plan_percentage,
      baseline_plan_finish_date: data?.baseline_plan_finish || data?.plan_end_date,
    }
    delete obj?.forecast_finish
    delete obj?.forecasted_end_date
    delete obj?.rev_plan_percentage
    delete obj?.revised_plan_percentage
    delete obj?.actual_percentage
    delete obj?.actual_plan_percentage
    delete obj?.baseline_plan_finish
    delete obj?.plan_end_date
    return obj
  }

  const getStatusProcess = useSelector((state: RootState) => state.status.getStatusProcess)
  const getOneStatusProcess = useSelector((state: RootState) => state.status.getOneStatusProcess)
  const statuses = useSelector((state: RootState) => state.status.statuses)
  const status = useSelector((state: RootState) => state.status.status)
  const progressTableVisibilities = useSelector((state: RootState) => state.status.progressTableVisibilities)
  const progressFilterValue = useSelector((state: RootState) => state.status.progressFilterValue)
  const renamePhaseProcess = useSelector((state: RootState) => state.status.renamePhaseProcess)
  const addPhaseProcess = useSelector((state: RootState) => state.status.addPhaseProcess)
  const progressTableColumnsWidth = useSelector((state: RootState) => state.status.progressTableColumnsWidth)
  const progressTableColumnsOrder = useSelector((state: RootState) => state.status.progressTableColumnsOrder)

  const getStatusApi = (data: { period: string; project_name?: string }) => dispatch(getStatus(data))
  const statusSortingApi = (data: any) => dispatch(statusSorting(data))
  const getOneStatusApi = (data: { id: string; period: string; project_name: string }) => dispatch(getOneStatus(data))
  const addStatusApi = (data: any) => dispatch(addStatus({ ...data, data: formatStatusData(data?.data) }))
  const updateStatusApi = (data: { id: number; data: any }) =>
    dispatch(updateStatus({ ...data, data: formatStatusData(data?.data) }))
  const updateTableStatusApi = (data: { id: number; data: any }) => dispatch(updateTableStatus(data))
  const deleteStatusApi = (data: number) => dispatch(deleteStatus(data))
  const setProgressTableColumnVisibilitiesApi = (data: any) => dispatch(setProgressTableColumnVisibilities(data))
  const setProgressFilterValueApi = (data: any) => dispatch(setProgressFilterValue(data))
  const setStatusByIdApi = (id: string) => dispatch(setStatusById(id))
  const setProgressTableColumnsWidthApi = (data: any) => dispatch(setProgressTableColumnsWidth(data))
  const setProgressTableColumnsOrderApi = (data: any) => dispatch(setProgressTableColumnsOrder(data))

  /*
   * Dispatch for phase
   */
  const deletePhaseApi = (id: number) => dispatch(deletePhase(id))
  const renamePhaseNameApi = (data: IRenamePhasePayload) => dispatch(renamePhaseName(data))
  const addPhaseNameApi = (data: IAddPhasePayload) => dispatch(addPhaseName(data))

  return {
    getStatusProcess,
    getOneStatusProcess,
    statuses,
    status,
    progressTableVisibilities,
    progressFilterValue,
    renamePhaseProcess,
    addPhaseProcess,
    progressTableColumnsWidth,
    progressTableColumnsOrder,
    setProgressTableColumnsWidthApi,
    setProgressTableColumnsOrderApi,
    statusSortingApi,
    getStatusApi,
    getOneStatusApi,
    addStatusApi,
    deleteStatusApi,
    updateStatusApi,
    setProgressTableColumnVisibilitiesApi,
    setProgressFilterValueApi,
    deletePhaseApi,
    renamePhaseNameApi,
    addPhaseNameApi,
    updateTableStatusApi,
    setStatusByIdApi,
  }
}

export default useStatus
