import { NextRouter } from 'next/router' // Assuming Next.js router
import { Routes } from '@/src/constant/enum'
import { IStatus } from '@/src/redux/status/interface'

export const redirectToStatus = (
  phaseId: number[],
  stageStatusId: number,
  subStageId: number | null,
  statuses: IStatus[],
  router: NextRouter,
): void => {
  let redirectStatus: IStatus | undefined
  // If subStageId is provided, filter by subStageId as well
  if (subStageId) {
    redirectStatus = statuses.find((item) => {
      return (
        (phaseId?.length === 0 || !phaseId
          ? item?.LookupProjectToPhase?.length === 0
            ? item?.LookupProjectToPhase?.length === 0
            : item?.LookupProjectToPhase?.some((lookup: any) => lookup?.phase === null)
          : !!item?.LookupProjectToPhase?.find((res) => phaseId.find((id) => id === res.id))) &&
        item?.MasterProjectStageStatus?.id === stageStatusId &&
        item?.MasterProjectSubStage?.id === subStageId
      )
    })
  } else {
    const test = [...statuses].sort((a, b) => {
      const aEmpty = !a.LookupProjectToPhase || a.LookupProjectToPhase.length === 0
      const bEmpty = !b.LookupProjectToPhase || b.LookupProjectToPhase.length === 0
      if (aEmpty === bEmpty) return 0
      return aEmpty ? -1 : 1
    })
    redirectStatus = test.find((item) => {
      return (
        (phaseId?.length === 0 || !phaseId
          ? item?.LookupProjectToPhase?.length === 0
            ? item?.LookupProjectToPhase?.length === 0
            : item?.LookupProjectToPhase?.some((lookup: any) => lookup?.phase === null)
          : !!item?.LookupProjectToPhase?.find((res) => phaseId?.find((id) => id === res.id))) &&
        item?.MasterProjectStageStatus?.id === stageStatusId
      )
    })
  }
  if (!redirectStatus) return

  const searchParam = router?.query?.search ? `?search=${router.query.search}` : ''
  router.push(`${Routes.EDIT_STATUS}/${redirectStatus.id}${searchParam}`)
}
