import { useMemo } from 'react'
import { generateMultiSelectOptionsFromPhases } from '../component/updateProgress/helper'
import useAuthorization from '../redux/authorization/useAuthorization'
import { ILookupProjectToPhase, IStatus } from '../redux/status/interface'
import {
  getUniqueValuesById,
  getUniqueValues,
  populateDropdownOptions,
  prepareDropdownOptions,
  prepareDropdownOptionsFromObject,
  sortArrayByKeyWithTypeConversion,
  getUniqueArrayByKey,
} from '../utils/arrayUtils'
import { getStageStatusByPermission } from '../utils/statusTab/stageStatusByPermission'

export const useOptions = (
  statuses: IStatus[],
  phaseIds: number[],
  currentUser: any,
  handleStatusUpdate: any,
  isStatusChanged?: boolean,
) => {
  return useMemo(() => {
    const statusesByPermission = statuses.filter((item: IStatus) =>
      getStageStatusByPermission(currentUser.role).includes(item.stage_status),
    )
    const sortedStatusesByPermission = sortArrayByKeyWithTypeConversion(
      statusesByPermission.map((status) => ({
        ...status,
        project_status_sorting_order: Number(status.project_status_sorting_order),
      })),
      'project_status_sorting_order',
      true,
    )

    const phases = populateDropdownOptions(sortedStatusesByPermission, 'LookupProjectToPhase')
    const uniquePhases = generateMultiSelectOptionsFromPhases(phases).map((item) => item.value) //*Exclude no phase
    const sanitizePhaseIds = phaseIds?.filter((id) => uniquePhases.includes(id)) || [] //*Remove No-phase Id

    const statusesByPhase = statuses.filter((item: any) => {
      return sanitizePhaseIds?.length === 0
        ? item?.LookupProjectToPhase?.length === 0 ||
            item?.LookupProjectToPhase?.some((lookup: any) => lookup?.phase === null)
        : !!item?.LookupProjectToPhase?.find((fi: ILookupProjectToPhase) =>
            sanitizePhaseIds?.find((id) => id === fi.id),
          )
    })

    const subStageOptions = statuses.filter((item, index) => {
      const value =
        phaseIds?.length === 0
          ? item?.LookupProjectToPhase?.length === 0 ||
            item?.LookupProjectToPhase?.some((lookup) => lookup?.phase === null)
          : item?.LookupProjectToPhase?.some((lookup) => phaseIds?.includes(lookup?.id))
      console.groupEnd()
      return value
    })

    const sorted = sortArrayByKeyWithTypeConversion(subStageOptions, 'project_status_sorting_order')
    const sortedStatusesByPhase = sortArrayByKeyWithTypeConversion(
      statusesByPhase.map((status: any) => ({
        ...status,
        project_status_sorting_order: Number(status.project_status_sorting_order),
      })),
      'project_status_sorting_order',
      true,
    )
    const stage_status_option = getUniqueArrayByKey(
      prepareDropdownOptionsFromObject(sortedStatusesByPhase, 'MasterProjectStageStatus', 'project_stage_status'),
      'label',
    )

    if (isStatusChanged) {
      handleStatusUpdate(statusesByPhase)
    }
    // Incomming changes for ignore sub_stage sorting
    /* const sub_stage_obj = sortedStatusesByPhase.map((item: any) => item?.MasterProjectSubStage?.project_sub_stage)
    const sub_stage = getUniqueValues(sub_stage_obj) */

    const sub_stage = getUniqueArrayByKey(
      [...prepareDropdownOptionsFromObject(sorted, 'MasterProjectSubStage', 'project_sub_stage')],
      'label',
    )

    return {
      phases,
      stage_status: stage_status_option,
      sub_stage,
    }
  }, [statuses, phaseIds, currentUser?.role, isStatusChanged])
}

export const getOptionsForRow = (rowData: any, statuses: IStatus[], currentUser: any) => {
  const { phase: selectedPhase, stage_status: selectedStageStatus } = rowData

  const statusesByPermission = statuses.filter((item: IStatus) =>
    getStageStatusByPermission(currentUser.role).includes(item.stage_status),
  )

  const sortedStatusesByPermission = sortArrayByKeyWithTypeConversion(
    statusesByPermission.map((status) => ({
      ...status,
      project_status_sorting_order: Number(status.project_status_sorting_order),
    })),
    'project_status_sorting_order',
    true,
  )

  const phaseOptions = getUniqueValues(populateDropdownOptions(sortedStatusesByPermission, 'phase'))

  const statusesByPhase = statusesByPermission.filter((status) => status.phase === selectedPhase)

  const sortedStatusesByPhase = sortArrayByKeyWithTypeConversion(
    statusesByPhase.map((status: any) => ({
      ...status,
      project_status_sorting_order: Number(status.project_status_sorting_order),
    })),
    'project_status_sorting_order',
    true,
  )

  const stage_status = getUniqueValues(populateDropdownOptions(sortedStatusesByPhase, 'stage_status'))

  const sub_stage = getUniqueValues(populateDropdownOptions(sortedStatusesByPhase, 'sub_stage'))

  return {
    phaseOptions,
    stage_status,
    sub_stage,
  }
}
