import { useEffect, useMemo, useState } from 'react'
import { CustomColumnDef } from '../interface'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const areObjectsIdentical = (obj1: any, obj2: any) => {
  if (!obj1 || !obj2 || obj1 === null || obj2 === null) return
  const keys1 = Object?.keys(obj1)
  const keys2 = Object?.keys(obj2)

  if (keys1.length !== keys2.length) return false
  return keys1.every((key) => JSON.stringify(obj1[key]) === JSON.stringify(obj2[key]))
}

const useTableConfig = (columns: CustomColumnDef<any>[], tableName: string) => {
  const [progressTableColumnsWidth, setProgressTableColumnsWidth] = useState<any>(null)
  const [progressTableColumnsOrder, setProgressTableColumnsOrder] = useState<any>(null)
  const [progressTableVisibilities, setProgressTableColumnVisibilities] = useState<any>(null)

  useEffect(() => {
    const columnsConfig = localStorage.getItem(tableName) as string
    if (columnsConfig) {
      const config = JSON.parse(columnsConfig)
      setProgressTableColumnsOrder(config?.columnOrder)
      setProgressTableColumnVisibilities(config?.columnVisibility)
      setProgressTableColumnsWidth(config?.columnWidth)
    } else {
      console.log(`${tableName} config not found in local storage. Setting default values.}`)
      const initialVisibleCols = Object.fromEntries(columns.map((col) => [col.accessorKey, col.visible ?? true]))
      setProgressTableColumnVisibilities(initialVisibleCols)
      const initialColWidth = Object.fromEntries(columns.map((col) => [col.accessorKey, col.size ?? 0]))
      setProgressTableColumnsWidth(initialColWidth)
    }
  }, [tableName])
  const [disableSaveConfigButton, setDisableSaveConfigButton] = useState<boolean>(true)

  useEffect(() => {
    const localStorageConfig = JSON.parse(localStorage.getItem(tableName) as string)
    const initialVisibleCols = Object?.fromEntries(columns.map((col) => [col.accessorKey, col.visible ?? true]))
    const initialColWidth = Object.fromEntries(columns.map((col) => [col.accessorKey, col.size ?? 0]))
    // const initialColOrder = Object.fromEntries(columns.map((col) => [col.accessorKey, col.size ?? 0]))

    const vis = progressTableVisibilities || {}
    const order = progressTableColumnsOrder || {}
    const width = progressTableColumnsWidth || {}

    let savedVis, savedOrder, savedWidth

    if (localStorageConfig) {
      // Compare with saved config
      savedVis = localStorageConfig.columnVisibility || {}
      savedOrder = localStorageConfig.columnOrder || {}
      savedWidth = localStorageConfig.columnWidth || {}
    } else {
      // Compare with initial defaults (before first save)
      savedVis = initialVisibleCols
      savedOrder = {} // default empty order
      savedWidth = initialColWidth // default empty widths
    }

    const isChanged =
      !areObjectsIdentical(vis, savedVis) ||
      !areObjectsIdentical(order, savedOrder) ||
      !areObjectsIdentical(width, savedWidth)

    setDisableSaveConfigButton(!isChanged)
  }, [progressTableColumnsOrder, progressTableColumnsWidth, progressTableVisibilities, columns, tableName])

  const handleSaveConfig = () => {
    try {
      // const columnConfig = {
      //   columnOrder:
      //     typeof progressTableColumnsOrder === 'object' && progressTableColumnsOrder !== null
      //       ? { ...progressTableColumnsOrder }
      //       : null,
      //   columnVisibility:
      //     typeof progressTableVisibilities === 'object' && progressTableVisibilities !== null
      //       ? { ...progressTableVisibilities }
      //       : null,
      //   columnWidth:
      //     typeof progressTableColumnsWidth === 'object' && progressTableColumnsWidth !== null
      //       ? { ...progressTableColumnsWidth }
      //       : null,
      // }
      const columnConfig = {
        columnOrder: progressTableColumnsOrder,
        columnVisibility: progressTableVisibilities,
        columnWidth: progressTableColumnsWidth,
      }
      localStorage.setItem(tableName, JSON.stringify(columnConfig))
      // Show success toast
      successToast('Table configuration saved successfully!')
      setDisableSaveConfigButton(true)
    } catch (error) {
      // Show error toast
      errorToast('Failed to save table configuration!')
    }
  }

  return {
    disableSaveConfigButton,
    handleSaveConfig,
    progressTableColumnsWidth,
    progressTableColumnsOrder,
    setProgressTableColumnsOrder,
    progressTableVisibilities,
    setProgressTableColumnVisibilities,
    setProgressTableColumnsWidth,
  }
}

export { useTableConfig }
