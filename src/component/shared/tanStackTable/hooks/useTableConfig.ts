import { useEffect, useMemo, useState } from 'react'
import { CustomColumnDef } from '../interface'

const areObjectsIdentical = (obj1: any, obj2: any) => {
  if (!obj1 || !obj2 || obj1 === null || obj2 === null) return
  const keys1 = Object?.keys(obj1)
  const keys2 = Object?.keys(obj2)

  if (keys1.length !== keys2.length) return false
  return keys1.every((key) => obj1[key] === obj2[key])
}

const useTableConfig = (columns: CustomColumnDef<any>[]) => {
  const [progressTableColumnsWidth, setProgressTableColumnsWidth] = useState<any>(null)
  const [progressTableColumnsOrder, setProgressTableColumnsOrder] = useState<any>(null)
  const [progressTableVisibilities, setProgressTableColumnVisibilities] = useState<any>(null)

  useEffect(() => {
    const columnsConfig = localStorage.getItem('ProgressTableConfig') as string
    console.log('columnsConfig: ', JSON.parse(columnsConfig))
    if (columnsConfig) {
      const config = JSON.parse(columnsConfig)
      setProgressTableColumnsOrder(config?.columnOrder)
      setProgressTableColumnVisibilities(config?.columnVisibility)
      setProgressTableColumnsWidth(config?.columnWidth)
      console.log('line------185------')
    } else {
      const initialVisibleCols = Object.fromEntries(columns.map((col) => [col.accessorKey, col.visible ?? true]))
      setProgressTableColumnVisibilities(initialVisibleCols)
      const initialColWidth = Object.fromEntries(columns.map((col) => [col.accessorKey, col.size ?? 0]))
      setProgressTableColumnsWidth(initialColWidth)
    }
  }, [])

  const disableSaveConfigButton = useMemo(() => {
    const localStorageConfig = JSON.parse(localStorage.getItem('ProgressTableConfig') as string)
    console.log('localStorageConfig: ', localStorageConfig)
    const initialVisibleCols = Object?.fromEntries(columns.map((col) => [col.accessorKey, col.visible ?? true]))
    const initialColWidth = Object.fromEntries(columns.map((col) => [col.accessorKey, col.size ?? 0]))

    const vis = progressTableVisibilities || {}
    console.log('vis: ', vis)
    const order = progressTableColumnsOrder || {}
    console.log('order: ', order)
    const width = progressTableColumnsWidth || {}
    console.log('width: ', width)

    let savedVis, savedOrder, savedWidth

    if (localStorageConfig) {
      // Compare with saved config
      savedVis = localStorageConfig.columnVisibility || {}
      savedOrder = localStorageConfig.columnOrder || {}
      savedWidth = localStorageConfig.columnWidth || {}
    } else {
      // Compare with initial defaults (before first save)
      savedVis = initialVisibleCols
      console.log('savedVis: ', savedVis, !areObjectsIdentical(vis, savedVis))
      savedOrder = {} // default empty order
      console.log('savedOrder: ', savedOrder, !areObjectsIdentical(order, savedOrder))
      savedWidth = initialColWidth // default empty widths
      console.log('savedWidth: ', savedWidth, !areObjectsIdentical(width, savedWidth))
    }

    const isChanged =
      !areObjectsIdentical(vis, savedVis) ||
      !areObjectsIdentical(order, savedOrder) ||
      !areObjectsIdentical(width, savedWidth)

    return !isChanged
  }, [progressTableColumnsOrder, progressTableColumnsWidth, progressTableVisibilities, columns])

  const handleSaveConfig = () => {
    const columnConfig = {
      columnOrder:
        typeof progressTableColumnsOrder === 'object' && progressTableColumnsOrder !== null
          ? { ...progressTableColumnsOrder }
          : null,
      columnVisibility:
        typeof progressTableVisibilities === 'object' && progressTableVisibilities !== null
          ? { ...progressTableVisibilities }
          : null,
      columnWidth:
        typeof progressTableColumnsWidth === 'object' && progressTableColumnsWidth !== null
          ? { ...progressTableColumnsWidth }
          : null,
    }
    localStorage.setItem('ProgressTableConfig', JSON.stringify(columnConfig))
  }

  return {
    disableSaveConfigButton,
    handleSaveConfig,
    progressTableColumnsWidth,
    progressTableColumnsOrder,
    setProgressTableColumnsOrder,
    progressTableVisibilities,
    setProgressTableColumnVisibilities,
    setProgressTableColumnsWidth,
  }
}

export { useTableConfig }
